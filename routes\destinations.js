const express = require('express');
const router = express.Router();
const Destination = require('../models/Destination');
const Account = require('../models/Account');

// GET /api/destinations - Get all destinations
router.get('/', async (req, res) => {
  try {
    const destinations = await Destination.find();
    res.json(destinations);
  } catch (error) {
    console.error('Error fetching destinations:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/destinations/account/:accountId - Get destinations for specific account
router.get('/account/:accountId', async (req, res) => {
  try {
    const { accountId } = req.params;
    
    // Verify account exists
    const account = await Account.findOne({ accountId });
    if (!account) {
      return res.status(404).json({ error: 'Account not found' });
    }
    
    const destinations = await Destination.find({ accountId });
    res.json(destinations);
  } catch (error) {
    console.error('Error fetching destinations for account:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/destinations/:id - Get destination by ID
router.get('/:id', async (req, res) => {
  try {
    const destination = await Destination.findById(req.params.id);
    if (!destination) {
      return res.status(404).json({ error: 'Destination not found' });
    }
    res.json(destination);
  } catch (error) {
    console.error('Error fetching destination:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /api/destinations - Create new destination
router.post('/', async (req, res) => {
  try {
    const { accountId, url, httpMethod, headers } = req.body;

    // Validate required fields
    if (!accountId || !url || !httpMethod || !headers) {
      return res.status(400).json({ 
        error: 'accountId, url, httpMethod, and headers are required fields' 
      });
    }

    // Verify account exists
    const account = await Account.findOne({ accountId });
    if (!account) {
      return res.status(404).json({ error: 'Account not found' });
    }

    // Convert headers object to Map if it's not already
    let headersMap;
    if (headers instanceof Map) {
      headersMap = headers;
    } else if (typeof headers === 'object') {
      headersMap = new Map(Object.entries(headers));
    } else {
      return res.status(400).json({ 
        error: 'Headers must be an object with key-value pairs' 
      });
    }

    const destination = new Destination({
      accountId,
      url,
      httpMethod: httpMethod.toUpperCase(),
      headers: headersMap
    });

    await destination.save();
    res.status(201).json(destination);
  } catch (error) {
    if (error.name === 'ValidationError') {
      return res.status(400).json({ 
        error: error.message 
      });
    }
    
    console.error('Error creating destination:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// PUT /api/destinations/:id - Update destination
router.put('/:id', async (req, res) => {
  try {
    const { url, httpMethod, headers } = req.body;
    
    const destination = await Destination.findById(req.params.id);
    if (!destination) {
      return res.status(404).json({ error: 'Destination not found' });
    }

    // Update fields if provided
    if (url !== undefined) destination.url = url;
    if (httpMethod !== undefined) destination.httpMethod = httpMethod.toUpperCase();
    if (headers !== undefined) {
      if (typeof headers === 'object') {
        destination.headers = new Map(Object.entries(headers));
      } else {
        return res.status(400).json({ 
          error: 'Headers must be an object with key-value pairs' 
        });
      }
    }

    await destination.save();
    res.json(destination);
  } catch (error) {
    if (error.name === 'ValidationError') {
      return res.status(400).json({ 
        error: error.message 
      });
    }
    
    console.error('Error updating destination:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// DELETE /api/destinations/:id - Delete destination
router.delete('/:id', async (req, res) => {
  try {
    const destination = await Destination.findById(req.params.id);
    if (!destination) {
      return res.status(404).json({ error: 'Destination not found' });
    }

    await Destination.findByIdAndDelete(req.params.id);
    res.json({ message: 'Destination deleted successfully' });
  } catch (error) {
    console.error('Error deleting destination:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
