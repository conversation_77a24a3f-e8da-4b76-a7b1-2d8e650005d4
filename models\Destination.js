const mongoose = require('mongoose');

const destinationSchema = new mongoose.Schema({
  accountId: {
    type: String,
    required: true,
    ref: 'Account'
  },
  url: {
    type: String,
    required: true,
    trim: true,
    validate: {
      validator: function(v) {
        return /^https?:\/\/.+/.test(v);
      },
      message: 'URL must be a valid URL starting with http:// or https://'
    }
  },
  httpMethod: {
    type: String,
    required: true,
    uppercase: true,
    enum: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
    default: 'POST'
  },
  headers: {
    type: Map,
    of: String,
    required: true,
    validate: {
      validator: function(v) {
        return v && v.size > 0;
      },
      message: 'Headers must contain at least one key-value pair'
    }
  }
}, {
  timestamps: true
});

// Index for faster lookups by account
destinationSchema.index({ accountId: 1 });

// Middleware to cascade delete destinations when account is deleted
destinationSchema.statics.deleteByAccountId = function(accountId) {
  return this.deleteMany({ accountId });
};

module.exports = mongoose.model('Destination', destinationSchema);
