{"name": "webhook-data-forwarder", "version": "1.0.0", "description": "Express web application to receive data and forward it to multiple destinations via webhooks", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["webhook", "express", "mongodb", "data-forwarding"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"axios": "^1.9.0", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.5.0", "express": "^4.19.2", "helmet": "^8.1.0", "mongoose": "^8.15.1", "morgan": "^1.10.0", "uuid": "^11.1.0"}, "devDependencies": {"nodemon": "^3.1.10"}}