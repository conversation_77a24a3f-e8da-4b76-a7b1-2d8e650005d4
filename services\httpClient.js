const axios = require('axios');

/**
 * Send data to a destination endpoint
 * @param {Object} destination - Destination configuration
 * @param {Object} data - Data to send
 * @returns {Promise} - Axios response promise
 */
const sendToDestination = async (destination, data) => {
  try {
    const { url, httpMethod, headers } = destination;
    
    // Convert headers Map to plain object
    const headersObj = {};
    if (headers instanceof Map) {
      headers.forEach((value, key) => {
        headersObj[key] = value;
      });
    } else {
      Object.assign(headersObj, headers);
    }

    const config = {
      method: httpMethod.toLowerCase(),
      url: url,
      headers: headersObj,
      timeout: 30000 // 30 seconds timeout
    };

    // Handle different HTTP methods
    if (httpMethod.toUpperCase() === 'GET') {
      // For GET requests, send data as query parameters
      config.params = data;
    } else {
      // For POST, PUT, PATCH, etc., send data in request body
      config.data = data;
    }

    console.log(`Sending data to ${url} via ${httpMethod}:`, data);
    
    const response = await axios(config);
    
    console.log(`Successfully sent to ${url}. Status: ${response.status}`);
    return response;
    
  } catch (error) {
    console.error(`Failed to send to ${destination.url}:`, error.message);
    throw error;
  }
};

/**
 * Send data to multiple destinations
 * @param {Array} destinations - Array of destination configurations
 * @param {Object} data - Data to send
 * @returns {Promise<Array>} - Array of results
 */
const sendToMultipleDestinations = async (destinations, data) => {
  const promises = destinations.map(async (destination) => {
    try {
      const response = await sendToDestination(destination, data);
      return {
        destination: destination.url,
        success: true,
        status: response.status,
        statusText: response.statusText
      };
    } catch (error) {
      return {
        destination: destination.url,
        success: false,
        error: error.message,
        status: error.response?.status || null
      };
    }
  });

  return Promise.all(promises);
};

module.exports = {
  sendToDestination,
  sendToMultipleDestinations
};
