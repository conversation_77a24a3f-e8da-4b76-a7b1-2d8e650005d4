const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const Destination = require('../models/Destination');
const { sendToMultipleDestinations } = require('../services/httpClient');

// POST /server/incoming_data - Receive and forward data
router.post('/incoming_data', authenticateToken, async (req, res) => {
  try {
    const data = req.body;
    const account = req.account;

    // Validate that data is JSON (Express already parses it if Content-Type is application/json)
    if (!data || typeof data !== 'object') {
      return res.status(400).json({ error: 'Invalid Data' });
    }

    console.log(`Received data for account ${account.accountId}:`, data);

    // Get all destinations for this account
    const destinations = await Destination.find({ accountId: account.accountId });

    if (destinations.length === 0) {
      console.log(`No destinations found for account ${account.accountId}`);
      return res.json({ 
        message: 'Data received successfully',
        account: account.accountName,
        destinationsCount: 0,
        note: 'No destinations configured for this account'
      });
    }

    // Send data to all destinations
    console.log(`Forwarding data to ${destinations.length} destinations for account ${account.accountId}`);
    
    const results = await sendToMultipleDestinations(destinations, data);
    
    // Count successful and failed deliveries
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;

    console.log(`Data forwarding completed. Successful: ${successful}, Failed: ${failed}`);

    res.json({
      message: 'Data received and forwarded',
      account: account.accountName,
      destinationsCount: destinations.length,
      successful,
      failed,
      results: results
    });

  } catch (error) {
    console.error('Error processing incoming data:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
