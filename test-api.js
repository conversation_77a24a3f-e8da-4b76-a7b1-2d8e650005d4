const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// Test data
const testAccount = {
  email: '<EMAIL>',
  accountName: 'Test Account',
  website: 'https://example.com'
};

const testDestination = {
  url: 'https://httpbin.org/post',
  httpMethod: 'POST',
  headers: {
    'APP_ID': '1234APPID1234',
    'APP_SECRET': 'test-secret-key',
    'ACTION': 'user.update',
    'Content-Type': 'application/json',
    'Accept': '*/*'
  }
};

const testData = {
  userId: 123,
  action: 'user.update',
  data: {
    name: '<PERSON>',
    email: '<EMAIL>',
    timestamp: new Date().toISOString()
  }
};

async function runTests() {
  try {
    console.log('🚀 Starting API Tests...\n');

    // Test 1: Create Account
    console.log('1. Creating account...');
    const accountResponse = await axios.post(`${BASE_URL}/api/accounts`, testAccount);
    console.log('✅ Account created:', accountResponse.data);
    const accountId = accountResponse.data.accountId;
    console.log('📝 Account ID:', accountId);
    console.log();

    // Test 2: Get Account by ID
    console.log('2. Getting account by ID...');
    const getAccountResponse = await axios.get(`${BASE_URL}/api/accounts/${accountId}`);
    console.log('✅ Account retrieved:', getAccountResponse.data);
    console.log();

    // Test 3: Create Destination
    console.log('3. Creating destination...');
    testDestination.accountId = accountId;
    const destinationResponse = await axios.post(`${BASE_URL}/api/destinations`, testDestination);
    console.log('✅ Destination created:', destinationResponse.data);
    const destinationId = destinationResponse.data._id;
    console.log();

    // Test 4: Get Destinations for Account
    console.log('4. Getting destinations for account...');
    const destinationsResponse = await axios.get(`${BASE_URL}/api/destinations/account/${accountId}`);
    console.log('✅ Destinations retrieved:', destinationsResponse.data);
    console.log();

    // Test 5: Get App Secret Token (we need to get it from the database since it's not returned in API)
    console.log('5. Getting account with secret token for testing...');
    // We'll need to get this from the database directly or create a special endpoint
    // For now, let's simulate getting the token
    const Account = require('./models/Account');
    const account = await Account.findOne({ accountId });
    const appSecretToken = account.appSecretToken;
    console.log('🔑 App Secret Token:', appSecretToken);
    console.log();

    // Test 6: Send Data to Webhook Handler
    console.log('6. Sending data to webhook handler...');
    const webhookResponse = await axios.post(`${BASE_URL}/server/incoming_data`, testData, {
      headers: {
        'Content-Type': 'application/json',
        'CL-X-TOKEN': appSecretToken
      }
    });
    console.log('✅ Data forwarded successfully:', webhookResponse.data);
    console.log();

    // Test 7: Test Authentication Error
    console.log('7. Testing authentication error...');
    try {
      await axios.post(`${BASE_URL}/server/incoming_data`, testData, {
        headers: {
          'Content-Type': 'application/json',
          'CL-X-TOKEN': 'invalid-token'
        }
      });
    } catch (error) {
      console.log('✅ Authentication error handled correctly:', error.response.data);
    }
    console.log();

    // Test 8: Test Invalid Data Error
    console.log('8. Testing invalid data error...');
    try {
      await axios.post(`${BASE_URL}/server/incoming_data`, 'invalid-json', {
        headers: {
          'Content-Type': 'application/json',
          'CL-X-TOKEN': appSecretToken
        }
      });
    } catch (error) {
      console.log('✅ Invalid data error handled correctly:', error.response.data);
    }
    console.log();

    // Cleanup
    console.log('9. Cleaning up test data...');
    await axios.delete(`${BASE_URL}/api/accounts/${accountId}`);
    console.log('✅ Test account and destinations deleted');
    console.log();

    console.log('🎉 All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = { runTests };
