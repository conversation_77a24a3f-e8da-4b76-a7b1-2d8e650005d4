{"info": {"name": "Webhook Data Forwarder API", "description": "Complete API collection for testing the Webhook Data Forwarder application", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "accountId", "value": "", "type": "string"}, {"key": "destinationId", "value": "", "type": "string"}, {"key": "appSecretToken", "value": "", "type": "string"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}}, {"name": "Account Management", "item": [{"name": "Create Account", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('accountId', response.accountId);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"accountName\": \"Test Account\",\n  \"website\": \"https://example.com\"\n}"}, "url": {"raw": "{{baseUrl}}/api/accounts", "host": ["{{baseUrl}}"], "path": ["api", "accounts"]}}}, {"name": "Get All Accounts", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/accounts", "host": ["{{baseUrl}}"], "path": ["api", "accounts"]}}}, {"name": "Get Account by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/accounts/{{accountId}}", "host": ["{{baseUrl}}"], "path": ["api", "accounts", "{{accountId}}"]}}}, {"name": "Update Account", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"accountName\": \"Updated Test Account\",\n  \"website\": \"https://updated-example.com\"\n}"}, "url": {"raw": "{{baseUrl}}/api/accounts/{{accountId}}", "host": ["{{baseUrl}}"], "path": ["api", "accounts", "{{accountId}}"]}}}, {"name": "Delete Account", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/accounts/{{accountId}}", "host": ["{{baseUrl}}"], "path": ["api", "accounts", "{{accountId}}"]}}}]}, {"name": "Destination Management", "item": [{"name": "Create Destination", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('destinationId', response._id);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"accountId\": \"{{accountId}}\",\n  \"url\": \"https://httpbin.org/post\",\n  \"httpMethod\": \"POST\",\n  \"headers\": {\n    \"APP_ID\": \"1234APPID1234\",\n    \"APP_SECRET\": \"test-secret-key\",\n    \"ACTION\": \"user.update\",\n    \"Content-Type\": \"application/json\",\n    \"Accept\": \"*/*\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/destinations", "host": ["{{baseUrl}}"], "path": ["api", "destinations"]}}}, {"name": "Get All Destinations", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/destinations", "host": ["{{baseUrl}}"], "path": ["api", "destinations"]}}}, {"name": "Get Destinations by Account ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/destinations/account/{{accountId}}", "host": ["{{baseUrl}}"], "path": ["api", "destinations", "account", "{{accountId}}"]}}}, {"name": "Get Destination by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/destinations/{{destinationId}}", "host": ["{{baseUrl}}"], "path": ["api", "destinations", "{{destinationId}}"]}}}, {"name": "Update Destination", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"url\": \"https://httpbin.org/put\",\n  \"httpMethod\": \"PUT\",\n  \"headers\": {\n    \"Authorization\": \"Bearer updated-token\",\n    \"Content-Type\": \"application/json\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/destinations/{{destinationId}}", "host": ["{{baseUrl}}"], "path": ["api", "destinations", "{{destinationId}}"]}}}, {"name": "Delete Destination", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/destinations/{{destinationId}}", "host": ["{{baseUrl}}"], "path": ["api", "destinations", "{{destinationId}}"]}}}]}, {"name": "Data Handler", "item": [{"name": "Send Data (Valid)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "CL-X-TOKEN", "value": "{{appSecretToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": 123,\n  \"action\": \"user.update\",\n  \"data\": {\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"timestamp\": \"2023-12-01T10:00:00Z\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/server/incoming_data", "host": ["{{baseUrl}}"], "path": ["server", "incoming_data"]}}}, {"name": "Send Data (Invalid Token)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "CL-X-TOKEN", "value": "invalid-token"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": 123,\n  \"action\": \"user.update\",\n  \"data\": {\n    \"name\": \"<PERSON>\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/server/incoming_data", "host": ["{{baseUrl}}"], "path": ["server", "incoming_data"]}}}, {"name": "Send Data (No Token)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": 123,\n  \"action\": \"user.update\",\n  \"data\": {\n    \"name\": \"<PERSON>\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/server/incoming_data", "host": ["{{baseUrl}}"], "path": ["server", "incoming_data"]}}}]}]}