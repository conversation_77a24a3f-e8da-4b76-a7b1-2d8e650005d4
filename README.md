# Webhook Data Forwarder

An Express.js web application that receives data and forwards it to multiple destinations via webhooks based on account configuration.

## Features

- **Account Management**: Create and manage accounts with unique identifiers and secret tokens
- **Destination Management**: Configure multiple webhook destinations per account
- **Data Forwarding**: Receive data via webhook and forward to configured destinations
- **Authentication**: Secure token-based authentication using app secret tokens
- **MongoDB Integration**: Persistent storage with Mongoose ODM
- **Error Handling**: Comprehensive error handling and validation

## Project Structure

```
├── config/
│   └── database.js          # MongoDB connection configuration
├── middleware/
│   └── auth.js              # Authentication middleware
├── models/
│   ├── Account.js           # Account schema and model
│   └── Destination.js       # Destination schema and model
├── routes/
│   ├── accounts.js          # Account CRUD operations
│   ├── destinations.js      # Destination CRUD operations
│   └── dataHandler.js       # Incoming data handler
├── services/
│   └── httpClient.js        # HTTP client for forwarding data
├── utils/
│   └── generateToken.js     # Token generation utilities
├── .env                     # Environment variables
├── package.json             # Project dependencies
├── server.js                # Main application entry point
└── README.md                # This file
```

## Installation

1. **Install Dependencies:**

   ```bash
   npm install
   ```

2. **Set up MongoDB:**

   - Install MongoDB Community Server from: https://www.mongodb.com/try/download/community
   - Start MongoDB service:
     - Windows: `net start MongoDB` (as Administrator)
     - macOS: `brew services start mongodb/brew/mongodb-community`
     - Linux: `sudo systemctl start mongod`

3. **Environment Configuration:**
   The `.env` file is already configured with:

   ```
   PORT=3000
   MONGODB_URI=mongodb://127.0.0.1:27017/webhook_app
   NODE_ENV=development
   ```

4. **Run the Application:**

   ```bash
   # Development mode with auto-reload
   npm run dev

   # Production mode
   npm start
   ```

5. **Verify Installation:**
   - Health check: http://localhost:3000/health
   - API documentation: http://localhost:3000/

## API Endpoints

### Account Management

#### Create Account

```http
POST /api/accounts
Content-Type: application/json

{
  "email": "<EMAIL>",
  "accountName": "My Account",
  "website": "https://example.com" // optional
}
```

#### Get All Accounts

```http
GET /api/accounts
```

#### Get Account by ID

```http
GET /api/accounts/{accountId}
```

#### Update Account

```http
PUT /api/accounts/{accountId}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "accountName": "Updated Account Name",
  "website": "https://newwebsite.com"
}
```

#### Delete Account

```http
DELETE /api/accounts/{accountId}
```

### Destination Management

#### Create Destination

```http
POST /api/destinations
Content-Type: application/json

{
  "accountId": "account-uuid",
  "url": "https://webhook.example.com/endpoint",
  "httpMethod": "POST",
  "headers": {
    "APP_ID": "1234APPID1234",
    "APP_SECRET": "secret-key",
    "ACTION": "user.update",
    "Content-Type": "application/json",
    "Accept": "*/*"
  }
}
```

#### Get All Destinations

```http
GET /api/destinations
```

#### Get Destinations by Account ID

```http
GET /api/destinations/account/{accountId}
```

#### Get Destination by ID

```http
GET /api/destinations/{destinationId}
```

#### Update Destination

```http
PUT /api/destinations/{destinationId}
Content-Type: application/json

{
  "url": "https://new-webhook.example.com/endpoint",
  "httpMethod": "PUT",
  "headers": {
    "Authorization": "Bearer token",
    "Content-Type": "application/json"
  }
}
```

#### Delete Destination

```http
DELETE /api/destinations/{destinationId}
```

### Data Handler

#### Receive and Forward Data

```http
POST /server/incoming_data
Content-Type: application/json
CL-X-TOKEN: {app-secret-token}

{
  "userId": 123,
  "action": "user.update",
  "data": {
    "name": "John Doe",
    "email": "<EMAIL>"
  }
}
```

## Authentication

The data handler endpoint requires authentication using the `CL-X-TOKEN` header. This token is the `appSecretToken` generated when creating an account.

## Data Forwarding Logic

1. **Authentication**: Validates the `CL-X-TOKEN` header against stored account tokens
2. **Account Identification**: Identifies the account based on the secret token
3. **Destination Lookup**: Retrieves all destinations configured for the account
4. **Data Forwarding**: Sends data to each destination using the configured HTTP method and headers
   - **GET requests**: Data is sent as query parameters
   - **POST/PUT/PATCH requests**: Data is sent in the request body as JSON

## Error Responses

- `400 Bad Request`: Invalid data or missing required fields
- `401 Unauthorized`: Missing or invalid authentication token
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server-side errors

## Environment Variables

- `PORT`: Server port (default: 3000)
- `MONGODB_URI`: MongoDB connection string
- `NODE_ENV`: Environment mode (development/production)

## Testing

### Using Postman

Import the `postman-collection.json` file into Postman for easy API testing.

### Manual Testing

1. Create an account and note the `accountId`
2. Create destinations for the account
3. Get the `appSecretToken` from the database (or create a special endpoint)
4. Send test data using the token

### Test Files

- `test-api.js` - Automated API testing script (requires MongoDB)
- `demo-without-db.js` - API documentation and examples
- `postman-collection.json` - Postman collection for manual testing

## Key Features Implemented

✅ **Account Module:**

- Unique email validation
- Auto-generated account ID (UUID)
- Auto-generated app secret token
- Optional website field
- CRUD operations

✅ **Destination Module:**

- Multiple destinations per account
- URL validation
- HTTP method support (GET, POST, PUT, PATCH, DELETE)
- Custom headers as key-value pairs
- CRUD operations
- Account-based filtering

✅ **Data Handler Module:**

- Token-based authentication via `CL-X-TOKEN` header
- JSON data validation
- Automatic account identification
- Data forwarding to all account destinations
- Different handling for GET vs POST/PUT methods
- Comprehensive error responses

✅ **Additional Features:**

- Cascade deletion (account deletion removes destinations)
- Security headers with Helmet
- CORS support
- Request logging with Morgan
- Comprehensive error handling
- MongoDB integration with Mongoose

## Development

```bash
# Install dependencies
npm install

# Run in development mode with auto-reload
npm run dev

# Run tests (when implemented)
npm test
```

## Production Deployment

1. Set up MongoDB in production environment
2. Update environment variables
3. Use PM2 or similar process manager:
   ```bash
   npm install -g pm2
   pm2 start server.js --name webhook-forwarder
   ```

## License

ISC
