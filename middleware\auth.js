const Account = require('../models/Account');

/**
 * Middleware to authenticate requests using CL-X-TOKEN header
 */
const authenticateToken = async (req, res, next) => {
  try {
    const token = req.headers['cl-x-token'];
    
    if (!token) {
      return res.status(401).json({ error: 'Un Authenticate' });
    }

    // Find account by app secret token
    const account = await Account.findOne({ appSecretToken: token });
    
    if (!account) {
      return res.status(401).json({ error: 'Un Authenticate' });
    }

    // Attach account to request object
    req.account = account;
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = {
  authenticateToken
};
