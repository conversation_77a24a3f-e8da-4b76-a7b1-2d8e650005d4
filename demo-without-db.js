const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function demonstrateAPI() {
  console.log('🚀 Webhook Data Forwarder API Demo\n');
  
  console.log('📋 Available Endpoints:');
  console.log('======================');
  
  try {
    // Test the root endpoint
    const rootResponse = await axios.get(BASE_URL);
    console.log('✅ Root endpoint response:');
    console.log(JSON.stringify(rootResponse.data, null, 2));
    console.log();
    
    // Test health endpoint
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health check response:');
    console.log(JSON.stringify(healthResponse.data, null, 2));
    console.log();
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
  
  console.log('📚 API Documentation:');
  console.log('====================');
  console.log();
  
  console.log('🏢 Account Management:');
  console.log('  POST   /api/accounts           - Create account');
  console.log('  GET    /api/accounts           - Get all accounts');
  console.log('  GET    /api/accounts/:id       - Get account by ID');
  console.log('  PUT    /api/accounts/:id       - Update account');
  console.log('  DELETE /api/accounts/:id       - Delete account');
  console.log();
  
  console.log('🎯 Destination Management:');
  console.log('  POST   /api/destinations                    - Create destination');
  console.log('  GET    /api/destinations                    - Get all destinations');
  console.log('  GET    /api/destinations/:id                - Get destination by ID');
  console.log('  GET    /api/destinations/account/:accountId - Get destinations for account');
  console.log('  PUT    /api/destinations/:id                - Update destination');
  console.log('  DELETE /api/destinations/:id                - Delete destination');
  console.log();
  
  console.log('📡 Data Handler:');
  console.log('  POST   /server/incoming_data   - Receive and forward data');
  console.log('         Header: CL-X-TOKEN (required for authentication)');
  console.log();
  
  console.log('📝 Example Usage:');
  console.log('================');
  console.log();
  
  console.log('1. Create Account:');
  console.log('   POST /api/accounts');
  console.log('   {');
  console.log('     "email": "<EMAIL>",');
  console.log('     "accountName": "My Account",');
  console.log('     "website": "https://example.com"');
  console.log('   }');
  console.log();
  
  console.log('2. Create Destination:');
  console.log('   POST /api/destinations');
  console.log('   {');
  console.log('     "accountId": "account-uuid",');
  console.log('     "url": "https://webhook.example.com/endpoint",');
  console.log('     "httpMethod": "POST",');
  console.log('     "headers": {');
  console.log('       "APP_ID": "1234APPID1234",');
  console.log('       "APP_SECRET": "secret-key",');
  console.log('       "Content-Type": "application/json"');
  console.log('     }');
  console.log('   }');
  console.log();
  
  console.log('3. Send Data:');
  console.log('   POST /server/incoming_data');
  console.log('   Headers: { "CL-X-TOKEN": "app-secret-token" }');
  console.log('   {');
  console.log('     "userId": 123,');
  console.log('     "action": "user.update",');
  console.log('     "data": { "name": "John Doe" }');
  console.log('   }');
  console.log();
  
  console.log('🔧 Setup Instructions:');
  console.log('======================');
  console.log('1. Install MongoDB: https://www.mongodb.com/try/download/community');
  console.log('2. Start MongoDB service');
  console.log('3. Run: npm run dev');
  console.log('4. Test the endpoints using the examples above');
  console.log();
  
  console.log('💡 Features:');
  console.log('============');
  console.log('✅ Account management with unique email and auto-generated secret tokens');
  console.log('✅ Multiple destinations per account');
  console.log('✅ Automatic data forwarding to all account destinations');
  console.log('✅ Support for different HTTP methods (GET, POST, PUT, etc.)');
  console.log('✅ Custom headers for each destination');
  console.log('✅ Token-based authentication');
  console.log('✅ Comprehensive error handling');
  console.log('✅ Cascade deletion (deleting account removes its destinations)');
  console.log();
}

demonstrateAPI();
