const mongoose = require("mongoose");

const connectDB = async () => {
  try {
    // Try to connect to MongoDB
    const conn = await mongoose.connect(process.env.MONGODB_URI, {
      serverSelectionTimeoutMS: 5000, // Timeout after 5s instead of 30s
    });
    console.log(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error("Database connection error:", error.message);
    console.log(
      "💡 To run this application, please ensure MongoDB is installed and running."
    );
    console.log(
      "💡 You can install MongoDB from: https://www.mongodb.com/try/download/community"
    );
    console.log(
      "💡 Or use MongoDB Atlas (cloud): https://www.mongodb.com/atlas"
    );
    process.exit(1);
  }
};

module.exports = connectDB;
