const mongoose = require("mongoose");
const { v4: uuidv4 } = require("uuid");
const { generateAppSecretToken } = require("../utils/generateToken");

const accountSchema = new mongoose.Schema(
  {
    accountId: {
      type: String,
      unique: true,
      default: () => uuidv4(),
      required: true,
    },
    email: {
      type: String,
      required: true,
      lowercase: true,
      trim: true,
      match: [
        /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
        "Please enter a valid email",
      ],
    },
    accountName: {
      type: String,
      required: true,
      trim: true,
    },
    appSecretToken: {
      type: String,
      default: generateAppSecretToken,
      required: true,
    },
    website: {
      type: String,
      trim: true,
      validate: {
        validator: function (v) {
          if (!v) return true; // Optional field
          return /^https?:\/\/.+/.test(v);
        },
        message:
          "Website must be a valid URL starting with http:// or https://",
      },
    },
  },
  {
    timestamps: true,
  }
);

// Index for faster lookups
accountSchema.index({ appSecretToken: 1 }, { unique: true });
accountSchema.index({ email: 1 }, { unique: true });

module.exports = mongoose.model("Account", accountSchema);
