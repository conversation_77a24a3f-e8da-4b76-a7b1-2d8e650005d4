const express = require('express');
const router = express.Router();
const Account = require('../models/Account');
const Destination = require('../models/Destination');

// GET /api/accounts - Get all accounts
router.get('/', async (req, res) => {
  try {
    const accounts = await Account.find().select('-appSecretToken');
    res.json(accounts);
  } catch (error) {
    console.error('Error fetching accounts:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/accounts/:id - Get account by ID
router.get('/:id', async (req, res) => {
  try {
    const account = await Account.findOne({ accountId: req.params.id }).select('-appSecretToken');
    if (!account) {
      return res.status(404).json({ error: 'Account not found' });
    }
    res.json(account);
  } catch (error) {
    console.error('Error fetching account:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /api/accounts - Create new account
router.post('/', async (req, res) => {
  try {
    const { email, accountName, website } = req.body;

    // Validate required fields
    if (!email || !accountName) {
      return res.status(400).json({ 
        error: 'Email and account name are required fields' 
      });
    }

    const account = new Account({
      email,
      accountName,
      website
    });

    await account.save();
    
    // Return account without exposing the secret token
    const accountResponse = account.toObject();
    delete accountResponse.appSecretToken;
    
    res.status(201).json(accountResponse);
  } catch (error) {
    if (error.code === 11000) {
      // Duplicate key error
      const field = Object.keys(error.keyPattern)[0];
      return res.status(400).json({ 
        error: `${field} already exists` 
      });
    }
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({ 
        error: error.message 
      });
    }
    
    console.error('Error creating account:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// PUT /api/accounts/:id - Update account
router.put('/:id', async (req, res) => {
  try {
    const { email, accountName, website } = req.body;
    
    const account = await Account.findOne({ accountId: req.params.id });
    if (!account) {
      return res.status(404).json({ error: 'Account not found' });
    }

    // Update fields if provided
    if (email !== undefined) account.email = email;
    if (accountName !== undefined) account.accountName = accountName;
    if (website !== undefined) account.website = website;

    await account.save();
    
    // Return account without exposing the secret token
    const accountResponse = account.toObject();
    delete accountResponse.appSecretToken;
    
    res.json(accountResponse);
  } catch (error) {
    if (error.code === 11000) {
      const field = Object.keys(error.keyPattern)[0];
      return res.status(400).json({ 
        error: `${field} already exists` 
      });
    }
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({ 
        error: error.message 
      });
    }
    
    console.error('Error updating account:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// DELETE /api/accounts/:id - Delete account and its destinations
router.delete('/:id', async (req, res) => {
  try {
    const account = await Account.findOne({ accountId: req.params.id });
    if (!account) {
      return res.status(404).json({ error: 'Account not found' });
    }

    // Delete all destinations for this account
    await Destination.deleteByAccountId(req.params.id);
    
    // Delete the account
    await Account.deleteOne({ accountId: req.params.id });
    
    res.json({ message: 'Account and associated destinations deleted successfully' });
  } catch (error) {
    console.error('Error deleting account:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
